<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Order\Components\OrderItemsForm;

use App\Model\Orm\Order\Delivery\DeliveryInformation;
use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Order\Delivery\LegacyDeliveryInformation;
use App\Model\Orm\Order\Delivery\OnlineDeliveryInformation;
use App\Model\Orm\Order\Delivery\PhysicalDeliveryInformation;
use App\Model\Orm\Order\Delivery\PickupDeliveryInformation;
use App\Model\Orm\Order\Delivery\StoreDeliveryInformation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

final readonly class OrderItemsFormSuccess
{

	public function __construct(
		private Orm $orm,
	)
	{
	}

	public function execute(Form $form, Order $order, User $user, ArrayHash $values): void
	{
		$attachedItems = $order->getBuyableOrderItems();
		$attachedItemsIds = [];
		foreach ($attachedItems as $key => $row) {
			$attachedItemsIds[$row->id] = $row;
		}

		foreach ($values->items as $id => $itemFormData) {
			if ($itemFormData->id) {
				$attachedOrderProduct = $this->orm->orderProduct->getById($itemFormData->id);
				if ($attachedOrderProduct) {
					unset($attachedItemsIds[$attachedOrderProduct->id]);
					$attachedOrderProduct->amount = $itemFormData->amount;
					$attachedOrderProduct->unitPrice = $itemFormData->unitPrice;
					$this->orm->persistAndFlush($attachedOrderProduct);
				}
			}

			foreach ($attachedItemsIds as $attachedItemsId) {
				$attachedOrderProduct = $this->orm->orderProduct->getById($attachedItemsId);
				$this->orm->productProduct->removeAndFlush($attachedOrderProduct);
			}
		}
	}
}
