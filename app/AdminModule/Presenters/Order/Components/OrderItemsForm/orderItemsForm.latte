{varType App\Model\Orm\Product\Product $object}

<form n:name="form">
	{control messageForForm, $flashes, $form}

	{var $props = [
	title: '<PERSON><PERSON><PERSON>',
	id: 'items',
	variant: 'main',
	icon: $templates.'/part/icons/align-left.svg',
	classes: ['u-mb-xxs'],
	dragdrop: false,
	tags: []
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

		{var $items = []}
		{foreach $form['items']->components as $productKey=>$productContainer}
				{continueIf $productKey === 'newItemMarker'}

				{var $url = $urls['searchProduct']->toArray()}
{*				{php $url['params']['mutationId'] = $mutationId}*}

				{var $item = [
					inps: [
						[
							input: $productContainer['name'],
							placeholder: 'Zadejte název produktu',
							data: [
								suggestinp-target: 'input',
							]
						],
						[
							input: $productContainer['amount'],
							placeholder: '<PERSON>adejte počet kusů',
							data: [
								suggestinp-target: 'input',
							]
						],
						[
							input: $productContainer['unitPrice'],
							placeholder: 'Zadejte jednotkovou cenu',
							data: [
								suggestinp-target: 'input',
							]
						],
						[
							input: $productContainer['id'],
							data: [
								suggestinp-target: 'idInput',
							],
							classes: 'u-hide',
							type: 'hidden'

						]
					],
					btnsAfter: [
						[
							icon: $templates.'/part/icons/trash.svg',
							tooltip: 'Odstranit',
							variant: 'remove',
							data: [
								action: 'RemoveItem#remove'
							],
						]
					],
					data: [
						controller: 'RemoveItem SuggestInp',
						removeitem-target: 'item',
						suggestinp-target: 'wrapper',
						suggestinp-url-value: Nette\Utils\Json::encode($url),
					]

				]}
				{php $items[] = $item}
			{/foreach}

			{include $templates.'/part/box/list.latte',
				props: [
					data: [
						controller: 'List',
						List-name-value: 'similar',
					],
					listData: [
						List-target: 'list',
					],
					addData: [
						action: 'List#add',
					],
					add: $disableEdit,
					dragdrop: true,
					type: 'input',
					items: $items
				]
			}

			{ifset $form['send']}
			<div class="grid grid--left grid--x-xs">
				<div class="grid__cell size--auto">
					<button n:name="send" class="btn btn--success">
						<span class="btn__text item-icon">
							<span class="item-icon__icon icon">
								{include $templates.'/part/icons/save.svg'}
							</span>
							<span class="item-icon__text">
								{_'save_button'}
							</span>
						</span>
					</button>
				</div>
			</div>
			{/ifset}

			<h2>Doprava a platba</h2>
			<table>
					<tbody>
						{*<tr n:foreach="$order->vouchers as $voucher">
							<td>{$voucher->getName()}</td>
							<td>{$voucher->amount}</td>
							<td>{$voucher->unitPrice->asMoney(4)|money}</td>
							<td>{$voucher->unitPriceVat|money}</td>
							<td>{$voucher->totalPrice|money}</td>
							<td>{$voucher->vatRate->value} <span n:if="$voucher->vatRateValue !== null">({$voucher->vatRateValue->toInt()}%)</span></td>
							<td>{$voucher->totalPriceVat|money}</td>
						</tr>
						<tr n:foreach="$order->gifts as $gift">
							<td>{$gift->getName()}</td>
							<td>{$gift->amount}</td>
							<td>{$gift->unitPrice->asMoney(4)|money}</td>
							<td>{$gift->unitPriceVat|money}</td>
							<td>{$gift->totalPrice|money}</td>
							<td>{$gift->vatRate->value} <span n:if="$gift->vatRateValue !== null">({$gift->vatRateValue->toInt()}%)</span></td>
							<td>{$gift->totalPriceVat|money}</td>
						</tr>*}
						<tr>
							<td colspan="4"><strong>Doprava:</strong> {$order->delivery->getName()}</td>
							<td>{$order->delivery->totalPrice|money}</td>
							<td>{$order->delivery->vatRate->value} <span n:if="$order->delivery->vatRateValue !== null">({$order->delivery->vatRateValue->toInt()}%)</span></td>
							<td>{$order->delivery->totalPriceVat|money}</td>
						</tr>
						<tr>
							<td colspan="4"><strong>Platba:</strong> {$order->payment->getName()}</td>
							<td>{$order->payment->totalPrice|money}</td>
							<td>{$order->payment->vatRate->value} <span n:if="$order->payment->vatRateValue !== null">({$order->payment->vatRateValue->toInt()}%)</span></td>
							<td>{$order->payment->totalPriceVat|money}</td>
						</tr>
					</tbody>

					<tfoot>
						<tr>
							<td colspan="6" align="right"><strong>K zaplacení celkem</strong></td>
							<th>
								{$order->getTotalPriceVat(withDelivery: true, includeGiftCertificates: true)|money}
							</th>
						</tr>
					</tfoot>
				</table>

	{/block}
{/embed}





</form>
