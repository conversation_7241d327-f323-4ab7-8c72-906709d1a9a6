<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Order\Components\OrderCancelForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\ConfigService;
use App\Model\Currency\CurrencyHelper;
use App\Model\CustomField\SuggestUrls;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class OrderCancelForm extends Control
{
	public function __construct(
		private readonly Order $order,
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly OrderModel $orderModel,
	)
	{
	}

	public function render(): void
	{
		$this->template->object = $this->order;
		$this->template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);

		$this->template->order = $this->order;
		$this->template->orm = $this->orm;
		$this->template->translatorDB = $this->translatorDB;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/orderCancelForm.latte');
	}

	protected function createComponentForm(): Form
	{
		$form = new Form();

		$form->setTranslator($this->translator);
		$form->addSelect('cancelReason', 'cancel_reason', Order::getConstsByPrefix('CANCEL_REASON'));
		$form->addSubmit('cancel');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$orderId = $this->order->id;
		//TODO add cancelReason
		$this->orderModel->cancel($this->order, true);
		$this->presenter->redirect('detail', ['id' => $orderId]);
	}

	public function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
