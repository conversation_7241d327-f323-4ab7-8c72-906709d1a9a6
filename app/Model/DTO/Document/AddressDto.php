<?php declare(strict_types=1);

namespace App\Model\DTO\Document;

readonly class AddressDto
{

	public string $firstName;
	public string $lastName;
	public string $zipNormalized;
	public string $phoneNumberNormalized;

	public function __construct(
		public string $fullName,
		public string $street,
		public string $city,
		public string $zip,
		public ?string $countryName,
		public ?string $phoneNumber,
		public ?string $companyName,
		public ?string $companyIdentifier,
		public ?string $vatNumber,
	)
	{
		$parts = explode(' ', $this->fullName);
		$this->firstName = array_shift($parts);
		$this->lastName = implode(' ', $parts);

		$this->zipNormalized = str_replace('-', '', $this->zip);
		$this->phoneNumberNormalized = preg_replace('/\s+/', '', $this->phoneNumber);
	}

}
