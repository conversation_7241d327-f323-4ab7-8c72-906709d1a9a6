<?php declare(strict_types=1);

namespace App\Model\DTO\Document;

use App\Model\Erp\Helper\NovikoTransportHelper;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\Payment\PaymentType;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Price;
use Brick\Money\Money;


readonly class DocumentDto
{

	public const string TYPE_ORDER = 'order';
	public const string TYPE_INVOICE = 'invoice';
	public const string TYPE_CREDIT_NOTE = 'credit_note';

	public AddressDto $customerAddress;

	public AddressDto $deliveryAddress;

	public AddressDto $supplierAddress;

	public bool $useDeliveryAddress;
	public string $number;
	public string $orderNumber;
	public int $novikoIdObjednavkaPart;
	public string $noteForStore;
	public string $note;


	public string $countryName;
	public string $countryIsoCode;
	public string $currencyIsoCode;
	public int $novikoTransportId;
	public ?string $pickupPointId;
	public bool $isCashOnDelivery;

	public Money $cashOnDeliveryPrice;

	public array $items;

	public function __construct(
		public string $type,
		public Order $order,
	)
	{
		$this->customerAddress = new AddressDto(
			$this->order->name,
			$this->order->street,
			$this->order->city,
			$this->order->zip,
			$this->order->state->name,
			$this->order->phone,
			$this->order->companyName,
			$this->order->companyIdentifier,
			$this->order->vatNumber,
		);

		$this->useDeliveryAddress = $this->order->useDeliveryAddress();
		if ($this->useDeliveryAddress) {
			$deliveryInformation = $this->order->delivery->information;
			$this->deliveryAddress = new AddressDto(
				$deliveryInformation->name ?? null,
				$deliveryInformation->street ?? null,
				$deliveryInformation->city ?? null,
				$deliveryInformation->zip ?? null,
				$deliveryInformation->state?->name ?? null,
				$deliveryInformation->phoneNumber ?? $this->order->phone,
				$deliveryInformation->company ?? null,
				null,
				null,
			);

		}

		//$this->supplierAddress = new AddressDto();

		$this->number = $this->order->orderNumber;
		$this->orderNumber = $this->order->orderNumber;
		$this->novikoIdObjednavkaPart = $this->order->novikoIdObjednavkaPart;

		$unionNotesForDriver = [];
		//TODO ?Honza - kde se ukládá poznímka pro řidiče? ->note je špatně
		if ($this->useDeliveryAddress && !empty($order->note)) {
			$unionNotesForDriver[] = "DA: " . $order->note;
		}
		if (!empty($order->note)) {
			$unionNotesForDriver[] = "FA: " . $order->note;
		}
		$this->noteForStore = implode(PHP_EOL, $unionNotesForDriver);

		$state = $this->order->mutation->getFirstState();
		$this->countryName = $state->name;
		$this->countryIsoCode = $this->order->country->code;
		$this->currencyIsoCode = $this->order->currency->getCurrencyCode();

		$this->isCashOnDelivery = $order->payment?->information->type === PaymentType::CashOnDelivery;
		if ($this->isCashOnDelivery) {
			$this->cashOnDeliveryPrice = $order->getTotalPriceWithDeliveryVat();
		} else {
			$this->cashOnDeliveryPrice = Money::of(0, $this->order->currency->getCurrencyCode());
		}

		//todo ?Luděk proč je to takto? obešel bych se místo $order->transport->novikoId
		//if (in_array($order->transportType, [Model\Order::TRANSPORT_PPL, Model\Order::TRANSPORT_PPL_PARCEL], true)) {
			$this->novikoTransportId = NovikoTransportHelper::getNovikoId($order->delivery->deliveryMethod->getDeliveryMethod());
		//} else {
			//$this->novikoTransportId = $order->transport->novikoId;
		//}

		$this->pickupPointId = $this->order->getPickupPointId();

		$items = [];
		foreach ($this->order->getItems() as $orderItem) {
			if ($orderItem instanceof ProductItem) {
				$items[$orderItem->id] = new DocumentItemDto($orderItem);
			}
		}
		$this->items = $items;
	}

}
