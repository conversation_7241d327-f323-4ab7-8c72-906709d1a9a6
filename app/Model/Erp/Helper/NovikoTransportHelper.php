<?php declare(strict_types = 1);

namespace App\Model\Erp\Helper;

use App\Model\Erp\Entity\ErpOrder as ErpOrder;
use App\Model\Orm\DeliveryMethod\CzechPost;
use App\Model\Orm\DeliveryMethod\CzechPostPickup;
use App\Model\Orm\DeliveryMethod\DeliveryMethod;
use App\Model\Orm\DeliveryMethod\DPD;
use App\Model\Orm\DeliveryMethod\DPDPickup;
use App\Model\Orm\DeliveryMethod\PPL;
use App\Model\Orm\DeliveryMethod\PPLPickup;
use App\Model\Orm\DeliveryMethod\Zasilkovna;
use App\Model\Orm\DeliveryMethod\ZasilkovnaPickup;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Enum\PaymentType;
use App\Model\Orm\Order\Order;

class NovikoTransportHelper
{
	public const int NOVIKO_ID_ZASILKOVNA = 41; // Zásilkovna
	public const int NOVIKO_ID_DPD = 6; // DPD
	public const int NOVIKO_ID_PPL = 50; // PPL
	public const int NOVIKO_ID_CESKA_POSTA = 12; // Ceska posta, Balikovna
	public const int NOVIKO_ID_DEFAULT = self::NOVIKO_ID_DPD; // DPD

	/*public const array NOVIKO_ID_PPL = [
		'cs' => 31,
		'sk' => 37,
		'pl' => 37,
	]; // na adresu Private, Connect

	public const array NOVIKO_ID_PPL_COD = [
		'cs' => 32,
		'sk' => 38,
		'pl' => 38,
	]; // na adresu Private, Connect, dobírka
	public const array NOVIKO_ID_PPL_PARCEL = [
		'cs' => 39,
		'sk' => 43,
		'pl' => 43,
	]; // parcel shop Smart
	public const array NOVIKO_ID_PPL_PARCEL_COD = [
		'cs' => 40,
		'sk' => 44,
		'pl' => 44,
	]; // parcel shop Smart, dobírka*/

	public static function getNovikoId(DeliveryMethod $deliveryMethod): int
	{
		return match ($deliveryMethod::class) {
			Zasilkovna::class, ZasilkovnaPickup::class => self::NOVIKO_ID_ZASIL.KOVNA,
			PPL::class, PPLPickup::class => self::NOVIKO_ID_PPL,
			DPD::class, DPDPickup::class => self::NOVIKO_ID_DPD,
			CzechPost::class, CzechPostPickup::class => self::NOVIKO_ID_CESKA_POSTA,
			default => throw new \LogicException('Unknown delivery method'),
		};
	}

	/*public static function pplDelivery(bool $isCashOnDelivery, string $countryCode = 'cs'): int
	{
		if ($isCashOnDelivery) {
			return self::NOVIKO_ID_PPL_COD[$countryCode];
		}
		return self::NOVIKO_ID_PPL[$countryCode];
	}

	public static function pplParcel(bool $isCashOnDelivery, string $countryCode = 'cs'): int
	{
		if ($isCashOnDelivery) {
			return self::NOVIKO_ID_PPL_PARCEL_COD[$countryCode];
		}
		return self::NOVIKO_ID_PPL_PARCEL[$countryCode];
	}*/

	/*public static function dpdDelivery(bool $isCashOnDelivery, string $countryCode = 'cs'): int
	{
		if ($isCashOnDelivery) {
			return self::NOVIKO_ID_DPD;
		}
		return self::NOVIKO_ID_DPD;
	}

	public static function dpdParcel(bool $isCashOnDelivery, string $countryCode = 'cs'): int
	{
		if ($isCashOnDelivery) {
			return self::NOVIKO_ID_DPD;
		}
		return self::NOVIKO_ID_DPD;
	}*/

}
