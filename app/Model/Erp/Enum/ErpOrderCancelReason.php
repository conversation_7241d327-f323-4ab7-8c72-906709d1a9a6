<?php

declare(strict_types=1);

namespace App\Model\Erp\Enum;

use App\Model\Orm\Traits\EnumToArray;

enum ErpOrderCancelReason: string
{

	use EnumToArray; //phpcs:ignore

	case Default = 'Default'; // cancelled at customer's request
	case ReturnedPackage = 'ReturnedPackage'; // undelivered package
	case LostPackage = 'LostPackage'; // lost package
	case DamagedPackage = 'DamagedPackage'; // damaged goods
	case NotInStock = 'NotInStock'; // item not in stock
	case PaymentTimedOut = 'PaymentTimedOut'; // online payment timed out
	case WithdrawContract = 'WithdrawContract'; // contract withdrawal
	case ComplaintGoods = 'ComplaintGoods'; // complaint/claim

	public function getLabel(): string
	{
		return match ($this) {
			self::Default => 'Cancelled at customer\'s request',
			self::ReturnedPackage => 'Undelivered package',
			self::LostPackage => 'Lost package',
			self::DamagedPackage => 'Damaged goods',
			self::NotInStock => 'Item not in stock',
			self::PaymentTimedOut => 'Online payment timed out',
			self::WithdrawContract => 'Contract withdrawal',
			self::ComplaintGoods => 'Complaint/claim',
		};
	}

}
