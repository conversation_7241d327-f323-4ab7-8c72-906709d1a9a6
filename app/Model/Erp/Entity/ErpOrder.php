<?php

namespace App\Model\Erp\Entity;

use App\Model\DTO\Document\DocumentDto;
use App\Model\Erp\Enum\ErpOrderStatus;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Product\ProductItem;
use Nette\SmartObject;
use stdClass;
use App\Model\Erp\Exception\NovikoException;
use App\Model\Erp\Helper\NovikoTransportHelper;
use App\Model\StringHelper;
use function trim;

class ErpOrder
{
	use SmartObject;


	/** @var array list of statuses */
	public array $statuses;

	/**
	 * List of item statuses for order items
	 * saveObjednavkaHD() returns status in <hdItemStatus> but getObjednavkaHD() in <status>
	 * @var array
	 */
	public static array $itemStatuses = [
		1 => 'OK',
		2 => 'Product does not exist',
		3 => 'Sales restriction',
		4 => 'Product ordered partially',
		5 => 'Not ordered - product not in stock',
		6 => 'Product is cancelled',
	];

	/** @var int MyCms\Order\Order->no */
	protected int $idObjednavkaPart = 0;

	/** @var string */
	protected string $adJmeno = '';

	/** @var string */
	protected string $adPrijmeni = '';

	/** @var string */
	protected string $adUlice = '';

	/** @var string */
	protected string $adMesto = '';

	/** @var string */
	protected string $adPsc = '';

	/** @var string */
	protected string $adSpolecnost = '';

	/** @var string */
	protected string $kontMail = '';

	/** @var string */
	protected string $kontTel = '';

	/** @var int Noviko ID for default carrier */
	protected int $kodDopravy;

	/** @var string|NULL Noviko ID of pickup point <KOD_POBOCKY>; if null, must not be in $data for Noviko */
	protected ?string $doprPobockaVyzv;

	/** @var string */
	protected string $cisloDL = '';

	/** @var bool */
	protected bool $doruceniDo12h = FALSE;

	/** @var string */
	protected string $adZedme = 'Česká Republika';

	/** @var string */
	protected string $adZemeIsoCode = 'CZ';

	/** @var float @note although optional, if missing in xml, it ends in Noviko with SQL error - cash on delivery cannot be NULL */
	protected float $dobirka = 0.0;

	/** @var string */
	protected string $menaIsoCode = 'CZK';

	/** @var bool FALSE = "reservation" -> unconfirmed order */
	protected bool $uzavrit = FALSE;

	/** @var string */
	protected string $poznProRidice = '';

	/** @var string format: "U{number of notes}: note number1;note number2;note number3" */
	protected string $poznProSklad = '';

	/** @var stdClass[] */
	protected stdClass $polozky;

	/** @var int|NULL */
	protected ?int $status;

	public function __construct()
	{
		// set defaults
		$this->statuses = ErpOrderStatus::getAllStatuses();
		$this->kodDopravy = NovikoTransportHelper::NOVIKO_ID_DEFAULT;
	}

	public function setDataFromResult(stdClass $data): void
	{
		foreach ($data as $prop => $val) {
			if ($val !== null && property_exists($this, $prop)) {
				if ($prop === 'kontTel') {
					$this->$prop = preg_replace('/\s+/', '', $val);
					continue;
				}

				$this->$prop = $val;
			}
		}
	}

	/**
	 * @return stdClass
	 */
	public function getData(): stdClass {
		$data = new stdClass();
		foreach ($this as $prop => $val) {
			// if it has NULL we cannot send to Noviko, e.g. doprPobockaVyzv
			if ($val !== NULL) {
				$data->$prop = $val;
			}
		}
		return $data;
	}

	/**
	 * @throws NovikoException
	 */
	public function setDataFromOrder(Order $order): void
	{
		$orderDto = new DocumentDto(DocumentDto::TYPE_ORDER, $order);

		// delivery address
		if ($orderDto->useDeliveryAddress) {
			$deliveryAddressDto = $orderDto->deliveryAddress;
		} else {
			$deliveryAddressDto = $orderDto->customerAddress;
		}

		// header
		$this->setIdObjednavkaPart($orderDto->novikoIdObjednavkaPart)
			->setAdJmeno($deliveryAddressDto->firstName)
			->setAdPrijmeni($deliveryAddressDto->lastName)
			->setAdUlice($deliveryAddressDto->street)
			->setAdMesto($deliveryAddressDto->city)
			->setAdPsc($deliveryAddressDto->zipNormalized)
			->setAdSpolecnost($deliveryAddressDto->companyName)
			->setKontMail($order->email)
			->setKontTel($deliveryAddressDto->phoneNumberNormalized)
			->setPoznProSklad($orderDto->noteForStore);

		$this->adZedme = $orderDto->countryName;
		$this->adZemeIsoCode = $orderDto->countryIsoCode ;
		$this->menaIsoCode = $orderDto->currencyIsoCode;

		// shipping
		if (empty($orderDto->novikoTransportId)) {
			throw new NovikoException('Unknown transport Noviko ID');
		}
		$this->setKodDopravy($orderDto->novikoTransportId);
		if (!empty($orderDto->pickupPointId)) {
			$this->setDoprPobockaVyzv($orderDto->pickupPointId);
		}

		// payment - cash on delivery
		if (!$orderDto->cashOnDeliveryPrice->isZero()) {
			$this->setDobirka($orderDto->cashOnDeliveryPrice->getAmount()->toFloat());
		}

		// items
		foreach ($order->getItems() as $orderItem) {
			// iterate through all items = everything with Noviko ID goes to Noviko (product, gift, sample, present)
			if ($orderItem instanceof ProductItem) {
				if (empty($orderItem->variant?->extId)) {
					continue;
				}
				$erpItem = new stdClass();
				$erpItem->idItemPart = $orderItem->id; // java.doc: Partner's item line ID
				$erpItem->kodZbo = $orderItem->variant->extId;
				$erpItem->pocet = $orderItem->amount;
				$this->polozky[] = $erpItem;
			} else {
				// TODO - if it's a package, break it down into items
			}
		}

		// other
		$this->setCisloDL($orderDto->orderNumber);

		$this->setUzavrit($order->readyToCloseInNoviko);
	}

	// ***************** setters ************************************************
	public function setIdObjednavkaPart(int $idObjednavkaPart): static
	{
		$this->idObjednavkaPart = $idObjednavkaPart;
		return $this;
	}

	public function setAdJmeno($adJmeno): static
	{
		$this->adJmeno = self::fixEncodingTranslit($adJmeno);
		return $this;
	}

	public function setAdPrijmeni($adPrijmeni): static
	{
		$this->adPrijmeni = self::fixEncodingTranslit($adPrijmeni);
		return $this;
	}

	public function setAdUlice($adUlice): static
	{
		$this->adUlice = self::fixEncodingTranslit($adUlice);
		return $this;
	}

	public function setAdMesto($adMesto): static
	{
		$this->adMesto = self::fixEncodingTranslit($adMesto);
		return $this;
	}

	public function setAdPsc($adPsc): static
	{
		$this->adPsc = self::fixEncodingTranslit($adPsc);
		return $this;
	}

	public function setAdSpolecnost($adSpolecnost): static
	{
		$this->adSpolecnost = self::fixEncodingTranslit($adSpolecnost);
		return $this;
	}

	public function setKontMail($kontMail): static
	{
		$this->kontMail = (string)$kontMail;
		return $this;
	}

	public function setKontTel($kontTel): static
	{
		$this->kontTel = (string)$kontTel;
		return $this;
	}

	public function setKodDopravy(int $kodDopravy): static {
		$this->kodDopravy = $kodDopravy;
		return $this;
	}

	public function setDoprPobockaVyzv(mixed $doprPobockaVyzv): static
	{
		if (empty($doprPobockaVyzv)) {
			$this->doprPobockaVyzv = null;
		} elseif (is_string($doprPobockaVyzv))  {
			$this->doprPobockaVyzv = (string)$doprPobockaVyzv;
		} else {
			$this->doprPobockaVyzv = (int)$doprPobockaVyzv;
		}

		return $this;
	}

	public function setCisloDL($cisloDL): static
	{
		$this->cisloDL = (string)$cisloDL;
		return $this;
	}

	public function setDoruceniDo12h($doruceniDo12h): static
	{
		$this->doruceniDo12h = (bool)$doruceniDo12h;
		return $this;
	}

	public function setDobirka($dobirka): static
	{
		$this->dobirka = (float)$dobirka;
		return $this;
	}

	public function setUzavrit($uzavrit): static
	{
		$this->uzavrit = (bool)$uzavrit;
		return $this;
	}

	public function setPoznProRidice($poznProRidice): static
	{
		$this->poznProRidice = trim((string)$poznProRidice);
		return $this;
	}

	public function setPoznProSklad($poznProSklad): static
	{
		$this->poznProSklad = trim((string)$poznProSklad);
		return $this;
	}

	/*public function setErpItems($erpItems): static
	{
		$this->erpItems = $erpItems instanceof ArrayHash ? (array)$erpItems : [0 => $erpItems];
		return $this;
	}*/

	public function setStatus($status): static
	{
		$this->status = StringHelper::intToNull($status);
		return $this;
	}

	public function getIdObjednavkaPart(): int
	{
		return $this->idObjednavkaPart;
	}

	public function getAdJmeno(): string
	{
		return $this->adJmeno;
	}

	public function getAdPrijmeni(): string
	{
		return $this->adPrijmeni;
	}

	public function getAdUlice(): string
	{
		return $this->adUlice;
	}

	public function getAdMesto(): string
	{
		return $this->adMesto;
	}

	public function getAdPsc(): string
	{
		return $this->adPsc;
	}

	public function getAdSpolecnost(): string
	{
		return $this->adSpolecnost;
	}

	public function getKontMail(): string
	{
		return $this->kontMail;
	}

	public function getKontTel(): string
	{
		return $this->kontTel;
	}

	public function getKodDopravy(): int
	{
		return $this->kodDopravy;
	}

	public function getDoprPobockaVyzv(): ?string
	{
		return $this->doprPobockaVyzv;
	}

	public function getCisloDL(): string
	{
		return $this->cisloDL;
	}

	public function getDoruceniDo12h(): bool
	{
		return $this->doruceniDo12h;
	}

	public function getAdZedme(): string
	{
		return $this->adZedme;
	}

	public function getAdZemeIsoCode(): string
	{
		return $this->adZemeIsoCode;
	}

	public function getDobirka(): float
	{
		return $this->dobirka;
	}

	public function getMenaIsoCode(): string
	{
		return $this->menaIsoCode;
	}

	public function getUzavrit(): bool
	{
		return $this->uzavrit;
	}

	public function getPoznProRidice(): string
	{
		return $this->poznProRidice;
	}

	public function getPoznProSklad(): string
	{
		return $this->poznProSklad;
	}

	public function getPolozky(): array
	{
		return $this->polozky;
	}

	public function getStatus(): ?int
	{
		return $this->status;
	}

	/**
	 * Prevede (prevypravi) nepovolene znaky ve windows-1250 do UTF-8
	 * http://phpfashion.com/prevody-mezi-kodovanim
	 *
	 * @param string
	 * @return string
	 */
	public static function fixEncodingTranslit($s): string {
		$s = @iconv('UTF-8', 'WINDOWS-1250//TRANSLIT', $s);
		return @iconv('WINDOWS-1250', 'UTF-8', $s);
	}
}
