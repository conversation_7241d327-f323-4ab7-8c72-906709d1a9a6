<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Console\Erp\CommandType;
use App\Model\Erp\Entity\ErpOrder;
use App\Model\Erp\Exception\LoggedException;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;
use Psr\Http\Message\ResponseInterface;
use stdClass;

class OrderConnector extends WsConnector implements ReadCollection
{

	protected function startup(LoggerManager $loggerManager): void
	{
		if ($this->config->verboseLog) {
			$this->logger = $loggerManager->get(CommandType::ORDER_IMPORT);
		}
		parent::startup($loggerManager);
	}

	public function getRows(Query $query): ArrayHash
	{
		return new ArrayHash();
	}


	public function getJsonSource(Query $query): ?string
	{
		return null;
	}

	public function getXmlSource(): ?string
	{
		return null;
	}

	public function saveOrder(ErpOrder $erpOrder): stdClass
	{
		return $this->callMethod(WsConnector::METHOD_SAVE_OBJEDNAVKA_HD, WsConnector::METHOD_CATEGORY_ORDER, ['data' => $erpOrder]);
	}

	public function getOrder (int $erpId, string $method = WsConnector::METHOD_GET_OBJEDNAVKA_HD): stdClass {
		$query = new stdClass();
		$query->erpId = $erpId;
		return $this->callMethod($method, WsConnector::METHOD_CATEGORY_ORDER, ['query' => $query]);
	}

}
