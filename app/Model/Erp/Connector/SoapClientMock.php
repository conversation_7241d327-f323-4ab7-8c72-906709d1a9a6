<?php /** @noinspection HttpUrlsUsage */
/** @noinspection PhpUnused */
declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Model\Erp\Enum\ErpOrderStatus;
use Exception;
use Nette\Utils\Json;
use RuntimeException;
use SimpleXMLElement;
use stdClass;

class SoapClientMock
{

	public function getVersion(): string
	{
		return 'ADAMiNT © HomeDelivery WebServices Interface v4.6.1 (User:TESThd)';
	}

	/**
	 * @throws Exception
	 */
	public function getZboziDetailList(): stdClass
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	/**
	 * @throws Exception
	 */
	public function getZboziStav(): stdClass
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	/**
	 * @throws Exception
	 */
	public function getZboziCS(): stdClass // phpcs:ignore
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	/**
	 * @throws Exception
	 */
	public function getZboziFoto(): stdClass // phpcs:ignore
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	// *********************************************************************

	/**
	 * @throws Exception
	 */
	private function loadXmlFromFile(string $method): stdClass
	{
		$filePath = TEMP_DIR . '/erp/' . $method . '.xml';

		if (file_exists($filePath)) {
			$xmlContent = file_get_contents($filePath);
			$xml = new SimpleXMLElement(strval($xmlContent));

			$xml->registerXPathNamespace('S', 'http://schemas.xmlsoap.org/soap/envelope/');
			$xml->registerXPathNamespace('ns2', 'http://ws.hd.modules.adamint.noviko.cz/');

			$responseNode = $xml->xpath('//ns2:' . $method . 'Response/return')[0] ?? null;

			if ($responseNode) {
				$json = Json::encode($responseNode);
				return Json::decode($json);
			}

			throw new RuntimeException('Could not parse SOAP response from file: ' . $filePath);
		}

		throw new RuntimeException('XML file not found: ' . $filePath);
	}

	public function saveObjednavkaHD(stdClass $data)
	{
		$result = new stdClass();
		$result->objednavkaOdb = new stdClass();
		$result->objednavkaOdb->id = 666;
		$result->objednavkaOdb->idHDObj = 555;
		$result->objednavkaOdb->idObjednavkaPart = 2507170020;
		$result->objednavkaOdb->uzavrit = true;

		$polozka1 = new stdClass;
		$polozka1->hdItemStatus = 5;
		$polozka1->id = "8b8cab4d349246c01f652160a80bec6e";
		$polozka1->kod_zbo = 54252;
		$polozka1->nazevZbozi = "Rogz hračka pes Míček GUMZ velký 7,8cm Červená 1ks";
		$polozka1->pocet = 1;
		$polozka1->pocetPozadovany = 1;

		$polozka2 = new stdClass;
		$polozka2->hdItemStatus = 2;
		$polozka2->id = "86340d5c171256223b8a5ef859dde5b6";
		$polozka2->kod_zbo = 44577;
		$polozka2->nazevZbozi = "Bazén sklád. nylon pes 120x30cm blue/red KAR 1ks";
		$polozka2->pocet = 2;
		$polozka2->pocetPozadovany = 1;

		$result->objednavkaOdb->polozky = [$polozka1, $polozka2];
		//$result->objednavkaOdb->polozky = $polozka1;  // když je v obj jen 1 produkt, tak <polozky> není pole, ale stdClass !

		$result->status = ErpOrderStatus::Open->value;

		return $result;
	}

}
