<?php
declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Product;

use App\Console\Erp\CommandType;
use App\Model\Currency\CurrencyHelper;
use App\Model\ElasticSearch;
use App\Model\Erp;
use App\Model\Erp\Connector\ImageConnector;
use App\Model\Erp\Connector\Query;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Messenger\Erp\ErpConsumer;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\LibraryImage\LibraryImageModel;
use App\Model\Orm\Orm;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\Supply\Supply;
use App\Model\StringHelper;
use Brick\Math\Exception\NumberFormatException;
use Brick\Math\Exception\RoundingNecessaryException;
use Brick\Money\Context\CustomContext;
use Brick\Money\Exception\UnknownCurrencyException;
use Brick\Money\Money;
use Contributte\Monolog\LoggerManager;
use RuntimeException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Throwable;
use Tracy\Debugger;
use Tracy\ILogger;

#[AsMessageHandler]
final class ProductConsumer extends ErpConsumer
{

	private ImageConnector $imageConnector;

	public function __construct(Orm $orm, MutationsHolder $mutationsHolder, MutationHolder $mutationHolder, PriceLevelModel $priceLevelModel, LibraryImageModel $libraryImageModel, LoggerManager $loggerManager, ElasticSearch\Product\Facade $esProductFacade, ElasticSearch\All\Facade $esAllFacade, ImageConnector $imageConnector)
	{
		parent::__construct($orm, $mutationsHolder, $mutationHolder, $priceLevelModel, $libraryImageModel, $loggerManager, $esProductFacade, $esAllFacade);
		$this->imageConnector = $imageConnector;
	}

	public const int USER_ERP_ID = 7;

	public const LIBRARY_IMPORT_PRODUCT_CATEGORY_ID = 5;

	protected string $loggerType = CommandType::PRODUCT_PROCESS;

	private Stock $defaultStock;

	private array $defaultPriceLevels;

	private ?Erp\Entity\Product $erpProduct = null;

	public function __invoke(ProductMessage $message): void
	{
		$this->checkMessageAndImport($message);
	}

	protected function setup(): void
	{
		$this->defaultStock = $this->orm->stock->getBy(['alias' => Stock::ALIAS_SHOP]);

		$this->defaultPriceLevels = $this->priceLevelModel->getAllPriceLevelByType([
			PriceLevel::TYPE_PURCHASE,
			PriceLevel::TYPE_RECOMMENDED,
		]);
	}

	/**
	 * @throws NumberFormatException
	 * @throws RoundingNecessaryException
	 * @throws SkippedException
	 * @throws UnknownCurrencyException
	 */
	protected function doImport(ImportCache $importCache): void
	{
		$this->dataCheck($importCache);

		$this->erpProduct = new Erp\Entity\Product($importCache->data);
		$this->setVariant($importCache);

		$this->syncVariant($importCache);
		$this->syncVariantLocalization();
	}

	/**
	 * @throws SkippedException
	 */
	private function dataCheck(ImportCache $importCache): void
	{
		if ($this->orm->importCache->findBy([
				'type' => $importCache->type,
				'status' => [ImportCache::STATUS_READY],
				'extId' => $importCache->extId,
			])->countStored() > 0) {
			throw new SkippedException('Newer import pending.');
		}
	}

	protected function save(ImportCache $importCache): void
	{
		if ($this->variant !== null) {
			$this->orm->persistAndFlush($this->variant);

			if ($this->hasUpdateElasticIndex()) {
				$this->esProductFacade->update($this->variant->product, $this->defaultMutation);
				$this->esAllFacade->updateAfterStockProcess($this->variant->product);
			}

			$this->variant->flushCache();
		}

		$this->orm->persistAndFlush($importCache);
	}

	/**
	 * @throws UnknownCurrencyException
	 * @throws NumberFormatException
	 * @throws RoundingNecessaryException
	 */
	private function syncVariant(ImportCache $importCache): void
	{
		if ($this->variant === null) { // only insert
			$this->variant = new ProductVariant();
			$this->orm->productVariant->attach($this->variant);

			$this->variant->extId = $this->erpProduct->id;
			$this->variant->created = $importCache->importedTime;
			$this->variant->createdBy = self::USER_ERP_ID;

			$this->addDefaultSuply($importCache);
			$this->addDefaultPrices($importCache);
			$this->syncImage($importCache);

		} else { // only update
			$this->variant->edited = $importCache->importedTime;
			$this->variant->editedBy = self::USER_ERP_ID;
		}

		// common
		$this->variant->ean = $this->erpProduct->ean;
		$this->variant->weight = $this->erpProduct->weight;
		$this->variant->weightUnit = $this->erpProduct->weightUnit;

		$this->variant->erpName = $this->erpProduct->name;
		$this->variant->erpContent = $this->erpProduct->content;
		$this->variant->erpDescription = $this->erpProduct->description;
		$this->variant->erpCategoryIds = $this->erpProduct->categoryIdsRaw;
		$this->variant->erpCategoryPath = $this->erpProduct->categoryPathRaw;
		$this->variant->erpVat = $this->erpProduct->vat;
	}

	private function syncVariantLocalization(): void
	{
		$variantLocalization = $this->variant->getLocalization($this->defaultMutation);

		if ($variantLocalization === null) { // only insert
			$variantLocalization = new ProductVariantLocalization();
			$this->orm->productVariantLocalization->attach($variantLocalization);

			$variantLocalization->variant = $this->variant;
			$variantLocalization->mutation = $this->defaultMutation;
			$variantLocalization->active = 1;
			$variantLocalization->name = $this->erpProduct->name;
		}
	}

	private function addDefaultSuply(ImportCache $importCache): void
	{
		$supply = new Supply();
		$this->orm->supply->attach($supply);

		$supply->stock = $this->defaultStock;
		$supply->variant = $this->variant;
		$supply->amount = $this->erpProduct->stock;
		$supply->lastImport = $importCache->importedTime;

		if ($supply->amount > 0) {
			$supply->lastOnStock = $importCache->importedTime;
		}
	}

	/**
	 * @throws RoundingNecessaryException
	 * @throws UnknownCurrencyException
	 * @throws NumberFormatException
	 */
	private function addDefaultPrices(ImportCache $importCache): void
	{
		$context = new CustomContext(4);

		// purchase
		$pricePurchase = new ProductVariantPrice();
		$this->orm->productVariantPrice->attach($pricePurchase);

		$pricePurchase->mutation = $this->defaultMutation;
		$pricePurchase->productVariant = $this->variant;
		$pricePurchase->priceLevel = $this->defaultPriceLevels[PriceLevel::TYPE_PURCHASE];
		$pricePurchase->price = Price::from(Money::of($this->erpProduct->purchasePrice, CurrencyHelper::CURRENCY_CZK, $context));
		$pricePurchase->vat = $this->erpProduct->vat;
		$pricePurchase->lastImport = $importCache->importedTime;

		// recommended
		$priceRecommended = new ProductVariantPrice();
		$this->orm->productVariantPrice->attach($priceRecommended);

		$priceRecommended->mutation = $this->defaultMutation;
		$priceRecommended->productVariant = $this->variant;
		$priceRecommended->priceLevel = $this->defaultPriceLevels[PriceLevel::TYPE_RECOMMENDED];
		$priceRecommended->price = Price::from(Money::of($this->erpProduct->recommendedPrice, CurrencyHelper::CURRENCY_CZK, $context));
		$priceRecommended->vat = $this->erpProduct->vat;
		$priceRecommended->lastImport = $importCache->importedTime;
	}

	private function syncImage(ImportCache $importCache): void
	{
		try {
			$result = $this->imageConnector->getRows(new Query(
				erpId: (int) $importCache->extId,
			));

			if (preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $result->foto)) {
				$this->logger->info('Detected possible base64 encoding, trying to decode');
				$decodedData = base64_decode($result->foto, true);
				if ($decodedData !== false) {
					$result->foto = $decodedData;
					$this->logger->info('Successfully decoded base64 data');
				}
			}

			if (isset($result->foto)) {
				$fileInfo = getimagesizefromstring($result->foto);

				if ($fileInfo === false) {
					$this->logger->info(sprintf('NO IMAGE | ERP ID %d', $importCache->extId));
					return;
				}

				$imageExt = StringHelper::getImageExtFromMimeType(intval($fileInfo[2]));
				$tmpFile = TEMP_DIR . '/import-tmp-image' . microtime(true) . $imageExt; // tmp file
				if (($fp = fopen($tmpFile, 'wb')) === false) {
					throw new RuntimeException('Cannot crate a temporary photo file: ' . $tmpFile);
				}

				fwrite($fp, $result->foto);
				fclose($fp);

				$name = sprintf('api-import-%s-%s', $importCache->extId, date('m-d-Y')) . $imageExt;
				$image = $this->libraryImageModel->addFromTmpFile($tmpFile, self::LIBRARY_IMPORT_PRODUCT_CATEGORY_ID, $name);

				@unlink($tmpFile);

				$this->variant->tempImageId = $image->id;
//				$this->logger->info(sprintf('ADDED IMAGE ID %s to PV | ERP ID %d', $image->id, $importCache->extId));
			}

		} catch (Throwable $e) {
			$this->logger->error(sprintf('IMAGE ERROR | ERP ID %d | %s', $importCache->extId, $e->getMessage()));
			Debugger::log($e, ILogger::EXCEPTION);
		}
	}

}
