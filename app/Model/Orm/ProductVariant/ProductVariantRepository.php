<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\Traits\HasSimpleSave;
use App\Model\Orm\User\User;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\IMapper;
use Nextras\Orm\Repository\IDependencyProvider;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductVariant|null getById($id)
 * @method ICollection<ProductVariant> findByIds(array $ids)
 * @method ICollection<ProductVariant> findByExactOrder(array $ids)
 * @method array findOrderedProductVariantIds(User $user)
 * @method array getErpCategoryList()
 * @method array getExtIdList()
 * @method ProductVariant save(?ProductVariant $entity, array $data)
 * @method ICollection<ProductVariant> searchByName($q, array $excluded = NULL, Mutation $mutation = null)
* // * @method ICollection<ProductVariant> getVariantIdsReviewedByUser(User $user, Mutation $mutation, ?string $status = ProductReview::STATUS_APPROVED)
 * @method ICollection<Product> getProductVariantWithFreeTransport(int|null $offset = NULL, int|null $limit = NULL)
 *
 * @extends Repository<ProductVariant>
 */
final class ProductVariantRepository extends Repository
{

	use HasSimpleSave;

	public function __construct(
		IMapper $mapper,
		?IDependencyProvider $dependencyProvider,
		private readonly ProductRepository $productRepository,
	)
	{
		parent::__construct($mapper, $dependencyProvider);
	}

	public static function getEntityClassNames(): array
	{
		return [ProductVariant::class];
	}

	/**
	 * @param string|int $id
	 * @return ProductVariant|null
	 */
	public function getActiveVariant($id): ?ProductVariant
	{
		$onlyPublicProduct = $this->productRepository->getPublicOnly();
		$orm = $this->getModel();
		assert($orm instanceof Orm);

		$mutation = $orm->getMutation();

		if ($onlyPublicProduct) {
			$variant = $this->findBy([
				'product->productLocalizations->public' => 1,
				'product->productLocalizations->mutation' => $mutation,

			])->getBy([
				'id' => $id,
				'variantLocalizations->mutation' => $mutation,
			]);
		} else {
			$variant = $this->getBy(['id' => $id]);
		}

		return $variant;
	}


	/**
	 * @param string|int|null $id
	 */
	public function getByExtId($id): ?ProductVariant
	{
		return isset($id) ? $this->getBy(['extId' => (string) $id]) : null;
	}

	/**
	 * @return ICollection<ProductVariant>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}
}
