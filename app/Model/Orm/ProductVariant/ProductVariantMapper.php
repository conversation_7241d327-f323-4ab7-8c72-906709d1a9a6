<?php

declare(strict_types = 1);

namespace App\Model\Orm\ProductVariant;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\User;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ProductVariant>
 */
final class ProductVariantMapper extends DbalMapper
{

	use HasCamelCase;
	use HasStaticCache;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'product_variant';
	}


	public function findOrderedProductVariantIds(User $user): array
	{
		return $this->loadCache($this->createCacheKey('orderedProductVariantIds', $user->id), function () use ($user) {
			$builder = $this->builder()->select('DISTINCT [pv.id]')->from('product_variant', 'pv');
			$builder->joinLeft('[order_product] as [op]', '[op.variantId] = [pv.id]');
			$builder->joinLeft('[order] as [o]', '[op.orderId] = [o.id]');
			$builder->where('[o.userId] = %i AND [o.state] NOT IN %s[]', $user->id, [OrderState::Draft, OrderState::Declined, OrderState::Canceled]);

			return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');
		});
	}

	/**
	 * @return ICollection<ProductVariant>
	 */
	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<ProductVariant>
	 */
	public function searchByName(string $q, ?array $excluded = null, ?Mutation $mutation = null): ICollection
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv');

		if ($mutation) {
			$builder->joinInner('[product_variant_localization] as pvl', 'pv.id = pvl.variantId and pvl.mutationId = %i', $mutation->id);
		} else {
			$builder->andWhere('erpName LIKE %_like_', $q);
		}

		if ($excluded) {
			$builder->andWhere('pv.id NOT IN %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	public function getErpCategoryList(): array
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv');

		$builder->andWhere('pv.erpCategoryPath != %s', '');
		$builder->groupBy('pv.erpCategoryPath');
		$builder->orderBy('pv.erpCategoryPath ASC');

		return $this->connection->queryByQueryBuilder($builder)->fetchPairs('erpCategoryPath', 'erpCategoryPath');
	}

	public function getExtIdList(): array
	{
		$builder = $this->builder()->select('pv.id as id, pv.extId as extId')->from($this->getTableName(), 'pv');
		return $this->connection->queryByQueryBuilder($builder)->fetchPairs('id', 'extId');
	}

	/**
	 * @return ICollection<ProductVariant>
	 */
	public function getProductVariantWithFreeTransport(int|null $offset = null, int|null $limit = null): ICollection
	{
		$builder = $this->builder()
			->select('pv.*')
			->from($this->getTableName(), 'pv')
			->where(
				'pv.isFreeTransport = 1
				OR
				(
					pv.isFreeTransportForced = 1
					AND (pv.freeTransportForcedFrom < NOW() OR pv.freeTransportForcedFrom IS NULL)
					AND (pv.freeTransportForcedTo > NOW() OR pv.freeTransportForcedTo IS NULL)
				)'
			)
			->limitBy($limit, $offset);

		return $this->toCollection($this->connection->queryByQueryBuilder($builder));
	}

//	public function getVariantIdsReviewedByUser(User $user, Mutation $mutation, ?string $status = ProductReview::STATUS_APPROVED): ICollection
//	{
//		$builder = $this->builder()->select('pv.*')
//			->from('product_review', 'pr')
//			->joinInner( '[product_variant] as pv', 'pv.productId = pr.productId')
//			->joinInner( '[product_variant_localization] as pvl', 'pvl.variantId = pv.id AND pvl.mutationId = %i', $mutation->id)
//			->joinInner( '[product_localization] as pl', 'pl.productId = pv.productId AND pl.mutationId = %i', $mutation->id)
//			->andWhere('pr.userId = %i', $user->id)
//			->andWhere('pl.public = 1 AND pvl.active = 1')
//			->orderBy('pr.created DESC');
//
//		if (isset($status)) {
//			$builder->andWhere('pr.status = %s', $status);
//		}
//
//		return $this->toCollection($builder);
//	}
}
