<?php declare(strict_types=1);

namespace App\Model\Price;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\PriceInfo;
use App\Model\PriceInfoFactory;
use Brick\Money\Money;

class ProductPriceModel
{

	public function __construct(
		private readonly ProductVariantPriceModel $productVariantPriceModel,
		private readonly PriceInfoFactory $priceInfoFactory,
	)
	{
	}

	public function getPriceInfo(Product $product, Mutation $mutation, PriceLevel $priceLevel, State $country): PriceInfo
	{
		if ($product->isCourse()) {
			return $product->firstVariant->getPriceInfo($mutation, $priceLevel, $country);
		}

		$priceInfoWithVariants = [];
		if ($product->activeVariants->count() === 1) {
			return $this->productVariantPriceModel->getPriceInfo($product->activeVariants->fetch(), $mutation, $priceLevel, $country);
		}

		foreach ($product->activeVariants as $productVariant) {
			$priceInfoWithVariants[] = new VariantPriceInfoWrapper(
				$productVariant,
				$this->productVariantPriceModel->getPriceInfo($productVariant, $mutation, $priceLevel, $country),
			);
		}

		$bestEnrichedPriceInfo = $this->getBestEnrichedPriceInfo($priceInfoWithVariants);

		if ($bestEnrichedPriceInfo === null) {
			return $this->getZeroPriceInfo($product, $mutation, $country);
		}

		return $bestEnrichedPriceInfo;
	}

	/**
	 * @param array<VariantPriceInfoWrapper> $priceInfoWithVariants
	 */
	private function getBestEnrichedPriceInfo(array $priceInfoWithVariants): ?PriceInfo
	{
		$bestProductPriceInfo = null;
		$hasDifferentPrices = false;
		array_walk($priceInfoWithVariants, function (VariantPriceInfoWrapper $priceInfoWithVariant) use (&$bestProductPriceInfo, &$hasDifferentPrices) {
			$priceInfo = $priceInfoWithVariant->variantPriceInfo;

// 			NOTE: disabled for now
//			$variant = $priceInfoWithVariant->productVariant;
//			if ( ! $variant->isInStock) {
//				return;
//			}
			if ($bestProductPriceInfo instanceof PriceInfo
				&& ! $bestProductPriceInfo->getSellingPriceVat()->isEqualTo($priceInfo->getSellingPriceVat())
				&& ! $priceInfo->getSellingPriceVat()->isZero()
			) {
				$hasDifferentPrices = true;
				if ($bestProductPriceInfo->getSellingPriceVat()->isGreaterThan($priceInfo->getSellingPriceVat())) {
					$bestProductPriceInfo = $priceInfo;
				}

			} elseif ($bestProductPriceInfo === null
				&& ! $priceInfo->getSellingPriceVat()->isZero()) {
				// init value
				$bestProductPriceInfo = $priceInfo;
			}
		});

		if ($bestProductPriceInfo === null) {
			return null;
		}

		$priceInfoForProduct = clone $bestProductPriceInfo;
		$priceInfoForProduct->setHasPriceFrom($hasDifferentPrices);
		return $priceInfoForProduct;
	}

	private function getZeroPriceInfo(Product $product, Mutation $mutation, State $country): PriceInfo
	{
		return $this->priceInfoFactory->create(
			$product->firstVariant,
			$mutation,
			$product->vatRate($country),
			$mutation->getSelectedCurrency()->getCurrencyCode(),
			Money::zero($mutation->getSelectedCurrency()),
			Money::zero($mutation->getSelectedCurrency()),
			Money::zero($mutation->getSelectedCurrency()),
			discountPricePeriod: ['from' => null, 'to' => null],
			discountPrice: null
		);
	}

}
