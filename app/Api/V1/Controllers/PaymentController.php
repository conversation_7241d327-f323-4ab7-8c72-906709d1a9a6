<?php declare(strict_types = 1);

namespace App\Api\V1\Controllers;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Model\Orm\CardPayment\CardPaymentProcessor;
use App\Model\Orm\CardPayment\PaymentGateway\GopayPaymentGateway;
use App\Model\Orm\CardPayment\PaymentGateway\EdenredPaymentGateway;
use App\Model\Orm\CardPayment\PaymentGateway\PluxeePaymentGateway;
use App\Model\Orm\Orm;
use Comgate\SDK\Entity\PaymentNotification;
use Nette\InvalidStateException;
use Tracy\Debugger;

#[Path('/payment')]
#[Tag('noAuthentication')]
#[Tag('Payment')]
final class PaymentController extends BaseV1Controller
{

	public function __construct(
		private readonly CardPaymentProcessor $cardPaymentProcessor,
		private readonly Orm $orm,
	)
	{
	}

	protected function redirect(string $path, array $query, ApiRequest $request, ApiResponse $response): ApiResponse
	{
		return $this->redirectResponse((string) $request->getUri()->withPath($path)->withQuery(http_build_query($query)), $response);
	}

	#[Path('/edenred-result')]
	#[Method('GET')]
	#[RequestParameter(name: 'ordernumber', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Reference ID of order (orderNumber)')]
	#[RequestParameter(name: 'statuscode', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Status code')]
	#[Response(description: 'Success', code: '200')]
	public function edenredResult(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$order = $this->orm->order->getBy(['orderNumber' => $request->getParameter('ordernumber')]);
		try {
			$this->cardPaymentProcessor->checkPayment(EdenredPaymentGateway::ID, $order->orderNumber, $request->getQueryParams());
		} catch (\Throwable $e) {
			// Log error
			Debugger::log($e);
		}

		$alias = $order->mutation->pages->step3->alias->alias;
		$query = ['orderId' => $order->id, 'orderHash' => $order->hash, 'paymentStatus' => EdenredPaymentGateway::getStatus((int) $request->getParameter('statuscode'))];
		return $this->redirect($alias, $query, $request, $response);
	}

	#[Path('/comgate-result')]
	#[Method('GET')]
	#[RequestParameter(name: 'refId', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Reference ID of order (orderNumber)')]
	#[RequestParameter(name: 'status', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Status - [PAID, CANCELLED, PENDING]')]
	#[Response(description: 'Success', code: '200')]
	#[Response(description: 'Order not found', code: '404')]
	public function comgateResult(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$order = $this->orm->order->getBy(['orderNumber' => $request->getParameter('refId')]);
		if ($order === null) {
			return $this->jsonResponse(['message' => 'Order not found.'], $response)->withStatus(404);
		}

		$alias = $order->mutation->pages->step3->alias->alias;
		$query = ['orderId' => $order->id, 'orderHash' => $order->hash, 'paymentStatus' => $request->getParameter('status')];

		return $this->redirect($alias, $query, $request, $response);
	}

	#[Path('/comgate')]
	#[Method('POST')]
	#[Response(description: 'Success', code: '200')]
	#[Response(description: 'Failed', code: '400')]
	public function comgate(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$data = $request->getParsedBody() ?? [];

		$notification = PaymentNotification::createFrom((array) $data);
		$transactionId = $notification->getTransactionId();

		try {
			$this->cardPaymentProcessor->checkPayment(GopayPaymentGateway::ID, $transactionId, (array) $data);
			return $this->jsonResponse(['status' => 'success'], $response);
		} catch (\Throwable $e) {
			// Log error
			Debugger::log($e);
			// return response
			return $this->jsonResponse(['status' => 'failed', 'message' => $e->getMessage()], $response)->withStatus(400);
		}
	}

	#[Path('/pluxee')]
	#[Method('GET')]
	#[RequestParameter(name: 'EShopOrderId', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Reference ID of order (orderNumber)')]
	#[RequestParameter(name: 'BenefitsPrice', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Benefit price')]
	#[RequestParameter(name: 'CompanyId', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Company ID')]
	#[RequestParameter(name: 'CompanyOrderId', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Company order ID')]
	#[RequestParameter(name: 'ApprovalCode', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Approval code')]
	#[Response(description: 'Success', code: '200')]
	public function pluxee(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$order = $this->orm->order->getBy(['orderNumber' => $request->getParameter('EShopOrderId')]);

		$alias = $order->mutation->pages->step3->alias->alias;
		$query = ['orderId' => $order->id, 'orderHash' => $order->hash];

		try {
			$this->cardPaymentProcessor->checkPayment(PluxeePaymentGateway::ID, $order->orderNumber, $request->getQueryParams());

			if (!$order->isPaid()) {
				throw new InvalidStateException('Payment failed.');
			}

		} catch (\Throwable $e) {
			// Log error
			Debugger::log($e);

			$query['paymentStatus'] = 'PENDING';
			$redirectUrl = (string) $request->getUri()->withPath($alias)->withQuery(http_build_query($query));
			return $this->plainResponse('FAIL2 unknown error' . "\n" . $redirectUrl, $response);
		}

		$redirectUrl = (string) $request->getUri()->withPath($alias)->withQuery(http_build_query($query));

		return $this->plainResponse($redirectUrl, $response); //$this->plainResponse('FAIL2 test', $response);
	}

	#[Path('/pluxee-fail')]
	#[Method('GET')]
	#[RequestParameter(name: 'EShopOrderId', type: 'string', in: EndpointParameter::IN_QUERY, required: true, description: 'Reference ID of order (orderNumber)')]
	#[Response(description: 'Success', code: '200')]
	public function pluxeeFail(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$order = $this->orm->order->getBy(['orderNumber' => $request->getParameter('EShopOrderId')]);

		$alias = $order->mutation->pages->step3->alias->alias;
		$query = ['orderId' => $order->id, 'orderHash' => $order->hash, 'paymentStatus' => 'PENDING'];

		try {
			$this->cardPaymentProcessor->checkPayment(PluxeePaymentGateway::ID, $order->orderNumber, $request->getQueryParams());
		} catch (\Throwable) {
			// do nothing
		}

		return $this->redirect($alias, $query, $request, $response);
	}

}
