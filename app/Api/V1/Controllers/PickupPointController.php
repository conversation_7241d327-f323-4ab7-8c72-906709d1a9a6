<?php declare(strict_types = 1);

namespace App\Api\V1\Controllers;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Entity\Response\PickupPointList;
use App\Model\Orm\Order\PickupPoint\PickupPointModel;
use JetBrains\PhpStorm\ArrayShape;
use Nextras\Dbal\Connection;

#[Path('/')]
#[Tag('noAuthentication')]
#[Tag('PickupPoint')]
final class PickupPointController extends BaseV1Controller
{

	public function __construct(
		private readonly PickupPointModel $pickupPointModel,
	)
	{
	}

	#[Path('/pickup-point')]
	#[Method('POST')]
	#[RequestParameter(name: 'deliveryMethodId', type: 'int', in: EndpointParameter::IN_QUERY, required: false, description: 'Delivery method ID')]
	#[RequestParameter(name: 'stateId', type: 'int', in: EndpointParameter::IN_QUERY, required: false, description: 'Filter states')]
	#[Response(description: 'Success', code: '200')]
	#[Response(description: 'POST data invalid', code: '400')]
	public function get(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$pickupPoints = $this->pickupPointModel->getClosestPlaces(
			postData: (array) $request->getParsedBody(),
			deliveryMethodId: $request->getParameter('deliveryMethodId'),
			stateId: $request->getParameter('stateId'),
		);

		$responseEntity = new PickupPointList($pickupPoints);
		return $this->jsonResponse($responseEntity->toResponse(), $response);
	}

	#[Path('/pickup-point-markers')]
	#[Method('GET')]
	#[RequestParameter(name: 'deliveryMethodId', type: 'int', in: EndpointParameter::IN_QUERY, required: false, description: 'Delivery method ID')]
	#[RequestParameter(name: 'stateId', type: 'int', in: EndpointParameter::IN_QUERY, required: false, description: 'Filter states')]
	public function getMarkers(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$pickupPoints = $this->pickupPointModel->getClosestPlaces(
			postData: (array) $request->getParsedBody(),
			deliveryMethodId: $request->getParameter('deliveryMethodId'),
			stateId: $request->getParameter('stateId'),
			onlyMarkers: true,
		);

		$responseEntity = new PickupPointList($pickupPoints, true);
		return $this->jsonResponse($responseEntity->toResponse(), $response);
	}

}
